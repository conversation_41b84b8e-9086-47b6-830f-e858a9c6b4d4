@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    --background: 12 6% 6%;
    --foreground: 0 0% 95%;

    --card: 12 6% 10%;
    --card-foreground: 0 0% 95%;

    --popover: 12 6% 8%;
    --popover-foreground: 0 0% 95%;

    --primary: 24 95% 53%;
    --primary-foreground: 0 0% 98%;

    --secondary: 12 6% 15%;
    --secondary-foreground: 0 0% 95%;

    --muted: 12 6% 15%;
    --muted-foreground: 0 0% 65%;

    --accent: 16 82% 47%;
    --accent-foreground: 0 0% 98%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;

    --border: 12 6% 15%;
    --input: 12 6% 15%;
    --ring: 24 95% 53%;

    --radius: 0.75rem;

    /* Pet Video Theme Colors */
    --brand-orange: 24 95% 53%;
    --brand-orange-hover: 24 95% 47%;
    --video-card: 12 6% 10%;
    --video-card-hover: 12 6% 12%;
    
    /* Gradients */
    --gradient-hero: linear-gradient(135deg, 12 6% 6%, 16 8% 8%);
    --gradient-overlay: linear-gradient(90deg, 12 6% 6% 0%, transparent 50%);
    --gradient-card: linear-gradient(145deg, 12 6% 8%, 12 6% 12%);
    
    /* Shadows */
    --shadow-card: 0 4px 20px -4px hsl(12 6% 4% / 0.5);
    --shadow-video: 0 8px 32px -8px hsl(24 95% 53% / 0.2);
    
    /* Animations */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.2s cubic-bezier(0.68, -0.55, 0.265, 1.55);

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 12 6% 6%;
    --foreground: 0 0% 95%;

    --card: 12 6% 10%;
    --card-foreground: 0 0% 95%;

    --popover: 12 6% 8%;
    --popover-foreground: 0 0% 95%;

    --primary: 24 95% 53%;
    --primary-foreground: 0 0% 98%;

    --secondary: 12 6% 15%;
    --secondary-foreground: 0 0% 95%;

    --muted: 12 6% 15%;
    --muted-foreground: 0 0% 65%;

    --accent: 16 82% 47%;
    --accent-foreground: 0 0% 98%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;

    --border: 12 6% 15%;
    --input: 12 6% 15%;
    --ring: 24 95% 53%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
  
  html {
    scroll-behavior: smooth;
  }
  
  /* Hide scrollbar for Chrome, Safari and Opera */
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Hide scrollbar for IE, Edge and Firefox */
  .scrollbar-hide {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
  }
  
  /* Text truncation */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}