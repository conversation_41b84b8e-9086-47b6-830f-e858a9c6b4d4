import { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { useNavigate } from 'react-router-dom';
import { Navigation } from '@/components/navigation';
import { Footer } from '@/components/footer';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { VideoGrid } from '@/components/VideoGrid';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { User, Heart, Star, Settings, Eye } from 'lucide-react';

export default function Profile() {
  const { user, userRole, loading } = useAuth();
  const navigate = useNavigate();
  const { toast } = useToast();
  
  const [profile, setProfile] = useState<any>(null);
  const [profileLoading, setProfileLoading] = useState(true);
  const [displayName, setDisplayName] = useState('');
  const [bio, setBio] = useState('');
  const [username, setUsername] = useState('');
  const [isEditing, setIsEditing] = useState(false);
  const [activeSection, setActiveSection] = useState('profile');

  useEffect(() => {
    if (!loading && !user) {
      navigate('/auth');
    }
  }, [user, loading, navigate]);

  useEffect(() => {
    if (user) {
      fetchProfile();
    }
  }, [user]);

  const fetchProfile = async () => {
    if (!user) return;
    
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('user_id', user.id)
        .single();

      if (error) throw error;

      setProfile(data);
      setDisplayName(data.full_name || '');
      setBio(data.bio || '');
      setUsername(data.username || '');
    } catch (error) {
      console.error('Error fetching profile:', error);
    } finally {
      setProfileLoading(false);
    }
  };

  const updateProfile = async () => {
    if (!user) return;

    try {
      const { error } = await supabase
        .from('profiles')
        .update({
          full_name: displayName,
          bio: bio,
          username: username
        })
        .eq('user_id', user.id);

      if (error) throw error;

      toast({
        title: "更新成功",
        description: "个人资料已更新",
      });
      
      setIsEditing(false);
      fetchProfile();
    } catch (error) {
      toast({
        variant: "destructive",
        title: "更新失败",
        description: "个人资料更新失败",
      });
    }
  };

  if (loading || profileLoading) {
    return (
      <div className="min-h-screen bg-background">
        <Navigation />
        <div className="flex items-center justify-center h-64 pt-24">
          <p>加载中...</p>
        </div>
        <Footer />
      </div>
    );
  }

  if (!user) return null;

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      
      <div className="max-w-7xl mx-auto px-4 py-8 pt-24">
        <div className="flex h-[calc(100vh-8rem)] border rounded-lg overflow-hidden bg-card">
          {/* 左侧菜单 */}
          <div className="w-64 bg-muted/30 border-r">
            <ProfileSidebar 
              userRole={userRole} 
              activeSection={activeSection} 
              onSectionChange={setActiveSection} 
            />
          </div>
          
          {/* 右侧内容区 */}
          <div className="flex-1 overflow-y-auto">
            <ProfileContent 
              section={activeSection}
              user={user}
              userRole={userRole}
              profile={profile}
              isEditing={isEditing}
              setIsEditing={setIsEditing}
              displayName={displayName}
              setDisplayName={setDisplayName}
              bio={bio}
              setBio={setBio}
              username={username}
              setUsername={setUsername}
              updateProfile={updateProfile}
            />
          </div>
        </div>
      </div>
      
      <Footer />
    </div>
  );
}

// Component for favorite videos
export function FavoriteVideos({ userId }: { userId: string }) {
  const [videos, setVideos] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchFavoriteVideos();
  }, [userId]);

  const fetchFavoriteVideos = async () => {
    try {
      const { data, error } = await supabase
        .from('favorites')
        .select(`
          video_id,
          videos (*)
        `)
        .eq('user_id', userId);

      if (error) throw error;

      const favoriteVideos = data.map(item => item.videos).filter(Boolean);
      setVideos(favoriteVideos);
    } catch (error) {
      console.error('Error fetching favorites:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) return <p>加载中...</p>;

  if (videos.length === 0) {
    return <p className="text-muted-foreground">您还没有收藏任何视频</p>;
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {videos.map((video: any) => (
        <Card key={video.id} className="overflow-hidden">
          <div className="relative aspect-video">
            <img 
              src={video.thumbnail_url} 
              alt={video.title}
              className="w-full h-full object-cover"
            />
            <div className="absolute bottom-2 right-2 bg-black/70 text-white px-2 py-1 rounded text-sm">
              {Math.floor(video.duration / 60)}:{(video.duration % 60).toString().padStart(2, '0')}
            </div>
          </div>
          <CardContent className="p-4">
            <h3 className="font-semibold text-sm line-clamp-2 mb-2">{video.title}</h3>
            <div className="flex items-center justify-between text-sm text-muted-foreground">
              <span className="flex items-center gap-1">
                <Eye className="w-4 h-4" />
                {video.view_count}
              </span>
              <span className="flex items-center gap-1">
                <Heart className="w-4 h-4" />
                {video.like_count}
              </span>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}

// Component for liked videos
export function LikedVideos({ userId }: { userId: string }) {
  const [videos, setVideos] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchLikedVideos();
  }, [userId]);

  const fetchLikedVideos = async () => {
    try {
      const { data, error } = await supabase
        .from('likes')
        .select(`
          video_id,
          videos (*)
        `)
        .eq('user_id', userId);

      if (error) throw error;

      const likedVideos = data.map(item => item.videos).filter(Boolean);
      setVideos(likedVideos);
    } catch (error) {
      console.error('Error fetching likes:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) return <p>加载中...</p>;

  if (videos.length === 0) {
    return <p className="text-muted-foreground">您还没有点赞任何视频</p>;
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {videos.map((video: any) => (
        <Card key={video.id} className="overflow-hidden">
          <div className="relative aspect-video">
            <img 
              src={video.thumbnail_url} 
              alt={video.title}
              className="w-full h-full object-cover"
            />
            <div className="absolute bottom-2 right-2 bg-black/70 text-white px-2 py-1 rounded text-sm">
              {Math.floor(video.duration / 60)}:{(video.duration % 60).toString().padStart(2, '0')}
            </div>
          </div>
          <CardContent className="p-4">
            <h3 className="font-semibold text-sm line-clamp-2 mb-2">{video.title}</h3>
            <div className="flex items-center justify-between text-sm text-muted-foreground">
              <span className="flex items-center gap-1">
                <Eye className="w-4 h-4" />
                {video.view_count}
              </span>
              <span className="flex items-center gap-1">
                <Heart className="w-4 h-4" />
                {video.like_count}
              </span>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}

// Admin Panel Component
function AdminPanel() {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>管理员面板</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardContent className="p-6">
                <div className="text-center">
                  <User className="h-8 w-8 mx-auto mb-2 text-primary" />
                  <h3 className="font-semibold">用户管理</h3>
                  <p className="text-sm text-muted-foreground">管理注册用户</p>
                  <Button className="mt-4 w-full" variant="outline">
                    查看用户
                  </Button>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <div className="text-center">
                  <Star className="h-8 w-8 mx-auto mb-2 text-primary" />
                  <h3 className="font-semibold">视频管理</h3>
                  <p className="text-sm text-muted-foreground">管理平台视频</p>
                  <Button className="mt-4 w-full" variant="outline">
                    查看视频
                  </Button>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <div className="text-center">
                  <Heart className="h-8 w-8 mx-auto mb-2 text-primary" />
                  <h3 className="font-semibold">评论管理</h3>
                  <p className="text-sm text-muted-foreground">管理用户评论</p>
                  <Button className="mt-4 w-full" variant="outline">
                    查看评论
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
