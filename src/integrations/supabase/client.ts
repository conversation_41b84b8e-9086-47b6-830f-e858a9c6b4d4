// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://mvprghbwhgndblguckdx.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im12cHJnaGJ3aGduZGJsZ3Vja2R4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQwMzEyNTEsImV4cCI6MjA2OTYwNzI1MX0.PRtri71Im7ddQRQMTDkGOlJWhiqm1FW8cvyBmHf-O-Y";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});