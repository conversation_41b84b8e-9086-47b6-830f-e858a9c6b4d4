import { VideoCard } from "@/components/ui/video-card";
import { ChevronLeft, ChevronRight, ChevronDown } from "lucide-react";
import { useRef, useState } from "react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface Video {
  id: string;
  title: string;
  category: string;
  year?: string;
  thumbnail: string;
}

interface VideoSectionProps {
  title: string;
  videos: Video[];
  className?: string;
  sectionId?: string;
  showViewMore?: boolean;
  onViewMore?: () => void;
}

export function VideoSection({ title, videos, className, sectionId, showViewMore = true, onViewMore }: VideoSectionProps) {
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [showLeftArrow, setShowLeftArrow] = useState(false);
  const [showRightArrow, setShowRightArrow] = useState(true);
  const [showAll, setShowAll] = useState(false);
  
  // 限制显示数量，默认显示4个
  const displayVideos = showAll ? videos : videos.slice(0, 4);

  const handleScroll = () => {
    if (scrollContainerRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = scrollContainerRef.current;
      setShowLeftArrow(scrollLeft > 0);
      setShowRightArrow(scrollLeft < scrollWidth - clientWidth - 10);
    }
  };

  const scroll = (direction: 'left' | 'right') => {
    if (scrollContainerRef.current) {
      const scrollAmount = 320; // Width of one card plus gap
      const newScrollLeft = direction === 'left' 
        ? scrollContainerRef.current.scrollLeft - scrollAmount
        : scrollContainerRef.current.scrollLeft + scrollAmount;
      
      scrollContainerRef.current.scrollTo({
        left: newScrollLeft,
        behavior: 'smooth'
      });
    }
  };

  return (
    <section id={sectionId} className={cn("relative group", className)}>
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-bold text-foreground">{title}</h2>
        {showViewMore && videos.length > 4 && (
          <Button
            variant="ghost"
            onClick={() => {
              if (showAll && onViewMore) {
                onViewMore();
              } else {
                setShowAll(!showAll);
              }
            }}
            className="text-primary hover:text-primary/80"
          >
            {showAll ? "收起" : `查看更多 (${videos.length - 4}+)`}
            <ChevronDown className={cn("w-4 h-4 ml-1 transition-transform", showAll && "rotate-180")} />
          </Button>
        )}
      </div>
      
      <div className="relative">
        {/* Left Arrow */}
        {showLeftArrow && (
          <button
            onClick={() => scroll('left')}
            className="absolute left-0 top-1/2 -translate-y-1/2 z-10 bg-black/80 hover:bg-black/90 text-white rounded-full p-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
          >
            <ChevronLeft className="w-6 h-6" />
          </button>
        )}
        
        {/* Right Arrow */}
        {showRightArrow && (
          <button
            onClick={() => scroll('right')}
            className="absolute right-0 top-1/2 -translate-y-1/2 z-10 bg-black/80 hover:bg-black/90 text-white rounded-full p-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
          >
            <ChevronRight className="w-6 h-6" />
          </button>
        )}
        
        {/* Video Cards Container */}
        <div
          ref={scrollContainerRef}
          onScroll={handleScroll}
          className="flex gap-4 overflow-x-auto scrollbar-hide pb-4"
          style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
        >
          {displayVideos.map((video) => (
            <VideoCard
              key={video.id}
              title={video.title}
              category={video.category}
              year={video.year}
              thumbnail={video.thumbnail}
              onPlay={() => console.log(`Playing ${video.title}`)}
            />
          ))}
        </div>
      </div>
    </section>
  );
}