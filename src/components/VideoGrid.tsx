import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Heart, MessageCircle, Eye, Star } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { useAuth } from '@/hooks/useAuth';
import { useToast } from '@/hooks/use-toast';

interface Video {
  id: string;
  title: string;
  description: string;
  video_url: string;
  thumbnail_url: string;
  category: string;
  tags: string[];
  duration: number;
  view_count: number;
  like_count: number;
  comment_count: number;
  uploaded_by?: string;
  created_at: string;
  updated_at: string;
}

interface VideoGridProps {
  category?: string;
}

export function VideoGrid({ category }: VideoGridProps) {
  const [videos, setVideos] = useState<Video[]>([]);
  const [loading, setLoading] = useState(true);
  const [likedVideos, setLikedVideos] = useState<Set<string>>(new Set());
  const [favoritedVideos, setFavoritedVideos] = useState<Set<string>>(new Set());
  const { user } = useAuth();
  const { toast } = useToast();

  useEffect(() => {
    fetchVideos();
    if (user) {
      fetchUserInteractions();
    }
  }, [category, user]);

  const fetchVideos = async () => {
    let query = supabase
      .from('videos')
      .select('*')
      .order('created_at', { ascending: false });

    if (category && category !== '全部') {
      query = query.eq('category', category);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Error fetching videos:', error);
    } else {
      // Map the database fields to our interface
      const mappedVideos = (data || []).map(video => ({
        id: video.id,
        title: video.title,
        description: video.description,
        video_url: video.video_url,
        thumbnail_url: video.thumbnail_url,
        category: video.category,
        tags: video.tags || [],
        duration: video.duration,
        view_count: video.view_count,
        like_count: video.like_count,
        comment_count: video.comment_count,
        uploaded_by: video.uploaded_by,
        created_at: video.created_at,
        updated_at: video.updated_at
      }));
      setVideos(mappedVideos);
    }
    setLoading(false);
  };

  const fetchUserInteractions = async () => {
    if (!user) return;

    // Fetch liked videos
    const { data: likes } = await supabase
      .from('likes')
      .select('video_id')
      .eq('user_id', user.id);

    if (likes) {
      setLikedVideos(new Set(likes.map(like => like.video_id)));
    }

    // Fetch favorited videos
    const { data: favorites } = await supabase
      .from('favorites')
      .select('video_id')
      .eq('user_id', user.id);

    if (favorites) {
      setFavoritedVideos(new Set(favorites.map(fav => fav.video_id)));
    }
  };

  const handleLike = async (videoId: string) => {
    if (!user) {
      toast({
        variant: "destructive",
        title: "请先登录",
        description: "登录后才能点赞视频",
      });
      return;
    }

    const isLiked = likedVideos.has(videoId);

    if (isLiked) {
      // Unlike
      const { error } = await supabase
        .from('likes')
        .delete()
        .eq('user_id', user.id)
        .eq('video_id', videoId);

      if (!error) {
        setLikedVideos(prev => {
          const newSet = new Set(prev);
          newSet.delete(videoId);
          return newSet;
        });
        // Update local state
        setVideos(prev => prev.map(video => 
          video.id === videoId 
            ? { ...video, like_count: video.like_count - 1 }
            : video
        ));
      }
    } else {
      // Like
      const { error } = await supabase
        .from('likes')
        .insert({ user_id: user.id, video_id: videoId });

      if (!error) {
        setLikedVideos(prev => new Set([...prev, videoId]));
        // Update local state
        setVideos(prev => prev.map(video => 
          video.id === videoId 
            ? { ...video, like_count: video.like_count + 1 }
            : video
        ));
      }
    }
  };

  const handleFavorite = async (videoId: string) => {
    if (!user) {
      toast({
        variant: "destructive",
        title: "请先登录",
        description: "登录后才能收藏视频",
      });
      return;
    }

    const isFavorited = favoritedVideos.has(videoId);

    if (isFavorited) {
      // Remove from favorites
      const { error } = await supabase
        .from('favorites')
        .delete()
        .eq('user_id', user.id)
        .eq('video_id', videoId);

      if (!error) {
        setFavoritedVideos(prev => {
          const newSet = new Set(prev);
          newSet.delete(videoId);
          return newSet;
        });
        toast({
          title: "已取消收藏",
          description: "视频已从收藏夹中移除",
        });
      }
    } else {
      // Add to favorites
      const { error } = await supabase
        .from('favorites')
        .insert({ user_id: user.id, video_id: videoId });

      if (!error) {
        setFavoritedVideos(prev => new Set([...prev, videoId]));
        toast({
          title: "收藏成功",
          description: "视频已添加到收藏夹",
        });
      }
    }
  };

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {Array.from({ length: 8 }).map((_, i) => (
          <Card key={i} className="animate-pulse">
            <div className="aspect-video bg-muted rounded-t-lg"></div>
            <CardContent className="p-4">
              <div className="h-4 bg-muted rounded mb-2"></div>
              <div className="h-3 bg-muted rounded mb-2"></div>
              <div className="flex gap-2">
                <div className="h-6 w-16 bg-muted rounded"></div>
                <div className="h-6 w-16 bg-muted rounded"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      {videos.map((video) => (
        <Card key={video.id} className="group hover:shadow-lg transition-all duration-300">
          <div className="relative aspect-video overflow-hidden rounded-t-lg">
            <img
              src={video.thumbnail_url}
              alt={video.title}
              className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
            />
            <div className="absolute bottom-2 right-2 bg-black/70 text-white px-2 py-1 rounded text-sm">
              {formatDuration(video.duration)}
            </div>
          </div>
          
          <CardContent className="p-4">
            <h3 className="font-semibold text-lg mb-2 line-clamp-2">{video.title}</h3>
            <p className="text-muted-foreground text-sm mb-3 line-clamp-2">{video.description}</p>
            
            <div className="flex flex-wrap gap-1 mb-3">
              {video.tags?.slice(0, 3).map((tag) => (
                <Badge key={tag} variant="outline" className="text-xs">
                  {tag}
                </Badge>
              ))}
            </div>

            <div className="flex items-center justify-between text-sm text-muted-foreground mb-3">
              <div className="flex items-center gap-4">
                <span className="flex items-center gap-1">
                  <Eye className="w-4 h-4" />
                  {video.view_count}
                </span>
                <span className="flex items-center gap-1">
                  <Heart className="w-4 h-4" />
                  {video.like_count}
                </span>
                <span className="flex items-center gap-1">
                  <MessageCircle className="w-4 h-4" />
                  {video.comment_count}
                </span>
              </div>
            </div>

            <div className="flex gap-2">
              <Button
                variant={likedVideos.has(video.id) ? "default" : "outline"}
                size="sm"
                onClick={() => handleLike(video.id)}
                className="flex-1"
              >
                <Heart className={`w-4 h-4 mr-1 ${likedVideos.has(video.id) ? 'fill-current' : ''}`} />
                点赞
              </Button>
              <Button
                variant={favoritedVideos.has(video.id) ? "default" : "outline"}
                size="sm"
                onClick={() => handleFavorite(video.id)}
                className="flex-1"
              >
                <Star className={`w-4 h-4 mr-1 ${favoritedVideos.has(video.id) ? 'fill-current' : ''}`} />
                收藏
              </Button>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}