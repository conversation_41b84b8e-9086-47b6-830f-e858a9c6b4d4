import { But<PERSON> } from "@/components/ui/button";
import { useAuth } from "@/hooks/useAuth";
import { <PERSON> } from "react-router-dom";
import { User, LogOut, Search, Bell } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

export function Navigation() {
  const { user, signOut } = useAuth();

  return (
    <nav className="fixed top-0 left-0 right-0 z-50 bg-background/95 backdrop-blur-sm border-b border-border">
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <div className="flex items-center space-x-8">
            <Link to="/">
              <h1 className="text-2xl font-bold text-primary">CoolPet</h1>
            </Link>
            
            {/* Navigation Links */}
            <div className="hidden md:flex items-center space-x-6">
              <a href="/#home" className="text-foreground hover:text-primary transition-colors">首页</a>
              <a href="/#trending" className="text-muted-foreground hover:text-foreground transition-colors">热门推荐</a>
              <a href="/#cute" className="text-muted-foreground hover:text-foreground transition-colors">本周热门</a>
              <a href="/#training" className="text-muted-foreground hover:text-foreground transition-colors">训练教程</a>
              {user && (
                <a href="/#" className="text-muted-foreground hover:text-foreground transition-colors">我的收藏</a>
              )}
            </div>
          </div>
          
          {/* Right Side Actions */}
          <div className="flex items-center space-x-4">
            <Button variant="ghost" size="icon">
              <Search className="w-5 h-5" />
            </Button>
            {user && (
              <Button variant="ghost" size="icon">
                <Bell className="w-5 h-5" />
              </Button>
            )}
            
            {user ? (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="icon">
                    <User className="w-5 h-5" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem asChild>
                    <Link to="/profile" className="w-full">
                      <User className="w-4 h-4 mr-2" />
                      个人中心
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={signOut}>
                    <LogOut className="w-4 h-4 mr-2" />
                    退出登录
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            ) : (
              <Link to="/auth">
                <Button variant="outline">登录</Button>
              </Link>
            )}
          </div>
        </div>
      </div>
    </nav>
  );
}
