import { Heart, Github, Mail, Phone } from "lucide-react";

export function Footer() {
  return (
    <footer className="bg-card border-t border-border mt-16">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Logo and Description */}
          <div className="col-span-1 md:col-span-2">
            <h3 className="text-2xl font-bold text-primary mb-4">CoolPet</h3>
            <p className="text-muted-foreground mb-4 max-w-md">
              最酷的宠物视频分享平台，汇集全球最可爱、最有趣的宠物瞬间。
              让每一个宠物故事都能被看见，让爱传递给更多人。
            </p>
            <div className="flex items-center text-sm text-muted-foreground">
              <span>Made with</span>
              <Heart className="w-4 h-4 mx-1 text-red-500 fill-current" />
              <span>for pet lovers everywhere</span>
            </div>
          </div>
          
          {/* Quick Links */}
          <div>
            <h4 className="font-semibold text-foreground mb-4">快速链接</h4>
            <ul className="space-y-2">
              <li>
                <a href="#home" className="text-muted-foreground hover:text-primary transition-colors">
                  首页
                </a>
              </li>
              <li>
                <a href="#trending" className="text-muted-foreground hover:text-primary transition-colors">
                  热门推荐
                </a>
              </li>
              <li>
                <a href="#cute" className="text-muted-foreground hover:text-primary transition-colors">
                  本周热门
                </a>
              </li>
              <li>
                <a href="#training" className="text-muted-foreground hover:text-primary transition-colors">
                  训练教程
                </a>
              </li>
            </ul>
          </div>
          
          {/* Contact */}
          <div>
            <h4 className="font-semibold text-foreground mb-4">联系我们</h4>
            <ul className="space-y-2">
              <li className="flex items-center text-muted-foreground">
                <Mail className="w-4 h-4 mr-2" />
                <span><EMAIL></span>
              </li>
              <li className="flex items-center text-muted-foreground">
                <Phone className="w-4 h-4 mr-2" />
                <span>************</span>
              </li>
              <li className="flex items-center text-muted-foreground">
                <Github className="w-4 h-4 mr-2" />
                <a href="#" className="hover:text-primary transition-colors">
                  开源项目
                </a>
              </li>
            </ul>
          </div>
        </div>
        
        {/* Bottom Bar */}
        <div className="border-t border-border mt-8 pt-8 flex flex-col sm:flex-row justify-between items-center">
          <p className="text-sm text-muted-foreground">
            © 2024 CoolPet. All rights reserved.
          </p>
          <div className="flex space-x-6 mt-4 sm:mt-0">
            <a href="#" className="text-sm text-muted-foreground hover:text-primary transition-colors">
              隐私政策
            </a>
            <a href="#" className="text-sm text-muted-foreground hover:text-primary transition-colors">
              服务条款
            </a>
            <a href="#" className="text-sm text-muted-foreground hover:text-primary transition-colors">
              Cookie政策
            </a>
          </div>
        </div>
      </div>
    </footer>
  );
}