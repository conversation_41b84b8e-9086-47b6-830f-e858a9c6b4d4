import { cn } from "@/lib/utils";
import { Play } from "lucide-react";
import { useState } from "react";

interface VideoCardProps {
  title: string;
  category: string;
  year?: string;
  thumbnail: string;
  className?: string;
  onPlay?: () => void;
}

export function VideoCard({ title, category, year, thumbnail, className, onPlay }: VideoCardProps) {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <div
      className={cn(
        "group relative bg-video-card rounded-lg overflow-hidden cursor-pointer transition-all duration-300",
        "hover:bg-video-card-hover hover:scale-105 hover:shadow-video",
        "min-w-[280px] sm:min-w-[320px]",
        className
      )}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onClick={onPlay}
    >
      <div className="aspect-video relative overflow-hidden">
        <img
          src={thumbnail}
          alt={title}
          className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
        />
        
        {/* Overlay */}
        <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
        
        {/* Play Button */}
        <div
          className={cn(
            "absolute inset-0 flex items-center justify-center",
            "opacity-0 group-hover:opacity-100 transition-all duration-300",
            isHovered ? "scale-100" : "scale-90"
          )}
        >
          <div className="bg-primary/90 rounded-full p-3 backdrop-blur-sm">
            <Play className="w-6 h-6 text-primary-foreground fill-primary-foreground" />
          </div>
        </div>
      </div>
      
      <div className="p-4">
        <h3 className="font-semibold text-foreground mb-1 line-clamp-2">{title}</h3>
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <span>{category}</span>
          {year && (
            <>
              <span>•</span>
              <span>{year}</span>
            </>
          )}
        </div>
      </div>
    </div>
  );
}