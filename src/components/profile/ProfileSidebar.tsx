import { User, Heart, Star, Settings, Video, MessageSquare, BarChart3, Users } from 'lucide-react';

interface ProfileSidebarProps {
  userRole: 'admin' | 'user' | null;
  activeSection: string;
  onSectionChange: (section: string) => void;
}

export function ProfileSidebar({ userRole, activeSection, onSectionChange }: ProfileSidebarProps) {
  const userMenuItems = [
    { id: 'profile', label: '个人资料', icon: User },
    { id: 'favorites', label: '收藏的视频', icon: Heart },
    { id: 'liked', label: '点赞的视频', icon: Star },
  ];

  const adminMenuItems = [
    { id: 'profile', label: '个人资料', icon: User },
    { id: 'favorites', label: '收藏的视频', icon: Heart },
    { id: 'liked', label: '点赞的视频', icon: Star },
    { id: 'divider', label: '', icon: null },
    { id: 'users', label: '用户管理', icon: Users },
    { id: 'videos', label: '视频管理', icon: Video },
    { id: 'comments', label: '评论管理', icon: MessageSquare },
    { id: 'analytics', label: '数据统计', icon: BarChart3 },
    { id: 'settings', label: '系统设置', icon: Settings },
  ];

  const menuItems = userRole === 'admin' ? adminMenuItems : userMenuItems;

  return (
    <nav className="p-4 space-y-2">
      <div className="mb-6">
        <h3 className="font-semibold text-lg mb-2">个人中心</h3>
        <p className="text-sm text-muted-foreground">
          {userRole === 'admin' ? '管理员面板' : '用户面板'}
        </p>
      </div>
      
      {menuItems.map((item) => {
        if (item.id === 'divider') {
          return <hr key="divider" className="my-4 border-border" />;
        }
        
        return (
          <button
            key={item.id}
            onClick={() => onSectionChange(item.id)}
            className={`w-full flex items-center gap-3 px-3 py-2 rounded-md text-left transition-colors ${
              activeSection === item.id 
                ? 'bg-primary text-primary-foreground' 
                : 'hover:bg-muted'
            }`}
          >
            {item.icon && <item.icon className="w-4 h-4" />}
            {item.label}
          </button>
        );
      })}
    </nav>
  );
}