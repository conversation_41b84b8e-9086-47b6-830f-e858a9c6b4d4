import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Settings } from 'lucide-react';

interface ProfileContentProps {
  section: string;
  user: any;
  userRole: string;
  profile: any;
  isEditing: boolean;
  setIsEditing: (editing: boolean) => void;
  displayName: string;
  setDisplayName: (name: string) => void;
  bio: string;
  setBio: (bio: string) => void;
  username: string;
  setUsername: (username: string) => void;
  updateProfile: () => void;
}

export function ProfileContent({ 
  section, 
  user, 
  userRole, 
  profile, 
  isEditing, 
  setIsEditing,
  displayName,
  setDisplayName,
  bio,
  setBio,
  username,
  setUsername,
  updateProfile
}: ProfileContentProps) {
  const renderContent = () => {
    switch (section) {
      case 'profile':
        return <ProfileSection />;
      case 'favorites':
        return <FavoriteVideos userId={user.id} />;
      case 'liked':
        return <LikedVideos userId={user.id} />;
      case 'users':
        return userRole === 'admin' ? <UsersManagement /> : <div>无权限访问</div>;
      case 'videos':
        return userRole === 'admin' ? <VideosManagement /> : <div>无权限访问</div>;
      case 'comments':
        return userRole === 'admin' ? <CommentsManagement /> : <div>无权限访问</div>;
      case 'analytics':
        return userRole === 'admin' ? <AnalyticsPanel /> : <div>无权限访问</div>;
      case 'settings':
        return userRole === 'admin' ? <SystemSettings /> : <div>无权限访问</div>;
      default:
        return <ProfileSection />;
    }
  };

  const ProfileSection = () => (
    <div className="p-6">
      <Card>
        <CardHeader>
          <CardTitle>个人资料</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-start gap-6">
            <Avatar className="h-20 w-20">
              <AvatarImage src={profile?.avatar_url} />
              <AvatarFallback>
                {profile?.full_name?.charAt(0) || profile?.username?.charAt(0) || 'U'}
              </AvatarFallback>
            </Avatar>
            
            <div className="flex-1 space-y-4">
              {isEditing ? (
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="displayName">昵称</Label>
                      <Input
                        id="displayName"
                        value={displayName}
                        onChange={(e) => setDisplayName(e.target.value)}
                        placeholder="请输入昵称"
                      />
                    </div>
                    <div>
                      <Label htmlFor="username">用户名</Label>
                      <Input
                        id="username"
                        value={username}
                        onChange={(e) => setUsername(e.target.value)}
                        placeholder="请输入用户名"
                      />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="bio">个人简介</Label>
                    <Input
                      id="bio"
                      value={bio}
                      onChange={(e) => setBio(e.target.value)}
                      placeholder="请输入个人简介"
                    />
                  </div>
                  <div className="flex gap-2">
                    <Button onClick={updateProfile}>保存</Button>
                    <Button variant="outline" onClick={() => setIsEditing(false)}>
                      取消
                    </Button>
                  </div>
                </div>
              ) : (
                <div>
                  <h2 className="text-xl font-semibold">{profile?.full_name || '未设置昵称'}</h2>
                  <p className="text-muted-foreground">@{profile?.username || '未设置用户名'}</p>
                  {profile?.bio && (
                    <p className="mt-2 text-sm">{profile.bio}</p>
                  )}
                  <p className="text-xs text-muted-foreground mt-2">
                    角色: {userRole === 'admin' ? '管理员' : '普通用户'}
                  </p>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="mt-2"
                    onClick={() => setIsEditing(true)}
                  >
                    <Settings className="h-4 w-4 mr-2" />
                    编辑资料
                  </Button>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  return renderContent();
}

// 管理员专用组件
const UsersManagement = () => (
  <div className="p-6">
    <Card>
      <CardHeader>
        <CardTitle>用户管理</CardTitle>
      </CardHeader>
      <CardContent>
        <p>用户管理功能开发中...</p>
      </CardContent>
    </Card>
  </div>
);

const VideosManagement = () => (
  <div className="p-6">
    <Card>
      <CardHeader>
        <CardTitle>视频管理</CardTitle>
      </CardHeader>
      <CardContent>
        <p>视频管理功能开发中...</p>
      </CardContent>
    </Card>
  </div>
);

const CommentsManagement = () => (
  <div className="p-6">
    <Card>
      <CardHeader>
        <CardTitle>评论管理</CardTitle>
      </CardHeader>
      <CardContent>
        <p>评论管理功能开发中...</p>
      </CardContent>
    </Card>
  </div>
);

const AnalyticsPanel = () => (
  <div className="p-6">
    <Card>
      <CardHeader>
        <CardTitle>数据统计</CardTitle>
      </CardHeader>
      <CardContent>
        <p>数据统计功能开发中...</p>
      </CardContent>
    </Card>
  </div>
);

const SystemSettings = () => (
  <div className="p-6">
    <Card>
      <CardHeader>
        <CardTitle>系统设置</CardTitle>
      </CardHeader>
      <CardContent>
        <p>系统设置功能开发中...</p>
      </CardContent>
    </Card>
  </div>
);