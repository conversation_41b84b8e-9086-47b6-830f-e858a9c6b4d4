import { But<PERSON> } from "@/components/ui/button";
import { Play, Info } from "lucide-react";
import heroImage from "@/assets/hero-pet.jpg";

export function HeroSection() {
  return (
    <section id="home" className="relative min-h-screen flex items-center">
      {/* Background Image */}
      <div className="absolute inset-0">
        <img
          src={heroImage}
          alt="Hero pet"
          className="w-full h-full object-cover"
        />
        <div className="absolute inset-0 bg-gradient-overlay" />
      </div>
      
      {/* Content */}
      <div className="relative z-10 container mx-auto px-4">
        <div className="max-w-2xl">
          <h1 className="text-5xl sm:text-6xl lg:text-7xl font-bold text-white mb-6 animate-fade-in">
            金毛的
            <br />
            快乐时光
          </h1>
          
          <p className="text-xl text-gray-200 mb-8 leading-relaxed animate-slide-up">
            观看这只可爱的金毛宝宝在阳光明媚的草地上尽情玩耍。
            每一个瞬间都充满了纯真的快乐，让您感受到宠物带来的无限温暖。
          </p>
          
          <div className="flex gap-4 animate-slide-up">
            <Button size="lg" className="bg-primary hover:bg-primary/90">
              <Play className="w-5 h-5 mr-2 fill-current" />
              立即观看
            </Button>
            <Button variant="secondary" size="lg" className="bg-white/20 text-white border-white/30 hover:bg-white/30">
              <Info className="w-5 h-5 mr-2" />
              了解更多
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}