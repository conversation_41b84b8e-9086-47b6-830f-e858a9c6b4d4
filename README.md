# Welcome to CoolPet!

## Project info

This is CoolPet - a pet video sharing platform built with modern web technologies.

### Key Features:

+ Pet video browsing with category filters (cats, dogs, training, funny)
+ User authentication and profiles with favorites/likes
+ Admin panel for content management
+ Responsive design with dark theme


### Tech Stack:

+ Frontend: React + TypeScript + Vite
+ UI: shadcn/ui components + Tailwind CSS
+ Backend: Supabase (database + auth)
+ Routing: React Router
+ State: TanStack Query

### Project Structure:

Main page (`src/pages/Index.tsx`) with hero section and video grid
Authentication system with user profiles
Component-based architecture with reusable UI elements
Chinese language interface targeting pet lovers


## Install and Run

Follow these steps:

```sh
# Step 1: Clone the repository using the project's Git URL.
git clone <YOUR_GIT_URL>

# Step 2: Navigate to the project directory.
cd <YOUR_PROJECT_NAME>

# Step 3: Install the necessary dependencies.
npm i

# Step 4: Start the development server with auto-reloading and an instant preview.
npm run dev
```



